################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
BSP/Src/%.o: ../BSP/Src/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/TI/CCSTUDIO_Theia/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"D:/000AAA_Embedded_Chips/Competition/TI_CUP/Track_Car_TI/TI_CAR/BSP/Inc" -I"D:/000AAA_Embedded_Chips/Competition/TI_CUP/Track_Car_TI/TI_CAR" -I"D:/000AAA_Embedded_Chips/Competition/TI_CUP/Track_Car_TI/TI_CAR/DMP" -I"D:/000AAA_Embedded_Chips/Competition/TI_CUP/Track_Car_TI/TI_CAR/App/Inc" -I"D:/000AAA_Embedded_Chips/Competition/TI_CUP/Track_Car_TI/TI_CAR/Debug" -I"D:/TI/CCSTUDIO_Theia/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include" -I"D:/TI/CCSTUDIO_Theia/mspm0_sdk_2_04_00_06/source" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"BSP/Src/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


